//
//  VisualIntelligenceIntents.swift
//  NIRA
//
//  Created by NIRA Team on 1/22/25.
//  iOS 26 Visual Intelligence App Intents
//

import Foundation
import SwiftUI

// iOS 26 App Intents for Visual Intelligence - will be available when iOS 26 is released
#if canImport(AppIntents) && canImport(VisualIntelligence)
import AppIntents
import VisualIntelligence
#endif

// MARK: - Visual Search Intent

#if canImport(AppIntents)
@available(iOS 26.0, *)
struct VisualSearchIntent: AppIntent {
    static var title: LocalizedStringResource = "Search Tamil Content"
    static var description = IntentDescription("Search for Tamil text, cultural elements, or learning materials in images")
    
    @Parameter(title: "Search Type", description: "Type of visual search to perform")
    var searchType: VisualSearchType
    
    @Parameter(title: "Image", description: "Image to search in")
    var image: IntentFile?
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let visualIntelligenceService = VisualIntelligenceService.shared
        
        guard let imageFile = image,
              let imageData = try? Data(contentsOf: imageFile.fileURL),
              let uiImage = UIImage(data: imageData) else {
            return .result(dialog: "Unable to process the image. Please try again.")
        }
        
        let request = VisualSearchRequest(
            image: uiImage,
            includeTextRecognition: searchType.includesTextRecognition,
            includeCulturalContent: searchType.includesCulturalContent,
            includeLearningMaterials: searchType.includesLearningMaterials,
            userContext: nil
        )
        
        let results = await visualIntelligenceService.handleVisualSearchRequest(request)
        
        if results.isEmpty {
            return .result(dialog: "No Tamil content found in this image.")
        }
        
        let resultSummary = formatSearchResults(results)
        return .result(dialog: resultSummary)
    }
    
    private func formatSearchResults(_ results: [VisualSearchResult]) -> String {
        var summary = "Found \(results.count) Tamil learning items:\n"
        
        for result in results.prefix(3) {
            switch result {
            case .tamilScript(let tamilResult):
                summary += "• Tamil text: \(tamilResult.text)\n"
            case .cultural(let culturalResult):
                summary += "• Cultural element: \(culturalResult.name)\n"
            case .learningMaterial(let materialResult):
                summary += "• Learning material: \(materialResult.topic)\n"
            }
        }
        
        return summary
    }
}

// MARK: - Tamil Text Recognition Intent

@available(iOS 26.0, *)
struct RecognizeTamilTextIntent: AppIntent {
    static var title: LocalizedStringResource = "Recognize Tamil Text"
    static var description = IntentDescription("Recognize and translate Tamil text in images")
    
    @Parameter(title: "Image", description: "Image containing Tamil text")
    var image: IntentFile?
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let visualIntelligenceService = VisualIntelligenceService.shared
        
        guard let imageFile = image,
              let imageData = try? Data(contentsOf: imageFile.fileURL),
              let uiImage = UIImage(data: imageData) else {
            return .result(dialog: "Unable to process the image. Please try again.")
        }
        
        do {
            let tamilResults = try await visualIntelligenceService.searchTamilScript(in: uiImage)
            
            if tamilResults.isEmpty {
                return .result(dialog: "No Tamil text found in this image.")
            }
            
            let bestResult = tamilResults.max { $0.confidence < $1.confidence }!
            let response = "Found Tamil text: \(bestResult.text)\nTranslation: \(bestResult.translation)"
            
            return .result(dialog: response)
            
        } catch {
            return .result(dialog: "Error recognizing Tamil text: \(error.localizedDescription)")
        }
    }
}

// MARK: - Cultural Content Discovery Intent

@available(iOS 26.0, *)
struct DiscoverCulturalContentIntent: AppIntent {
    static var title: LocalizedStringResource = "Discover Tamil Culture"
    static var description = IntentDescription("Discover Tamil cultural elements in images")
    
    @Parameter(title: "Image", description: "Image to analyze for cultural content")
    var image: IntentFile?
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let visualIntelligenceService = VisualIntelligenceService.shared
        
        guard let imageFile = image,
              let imageData = try? Data(contentsOf: imageFile.fileURL),
              let uiImage = UIImage(data: imageData) else {
            return .result(dialog: "Unable to process the image. Please try again.")
        }
        
        do {
            let culturalResults = try await visualIntelligenceService.searchCulturalContent(in: uiImage)
            
            if culturalResults.isEmpty {
                return .result(dialog: "No Tamil cultural elements found in this image.")
            }
            
            let bestResult = culturalResults.first!
            var response = "Found: \(bestResult.name)\n\(bestResult.description)"
            
            if bestResult.arExperience {
                response += "\n\nTap to explore in AR!"
            }
            
            return .result(dialog: response)
            
        } catch {
            return .result(dialog: "Error analyzing cultural content: \(error.localizedDescription)")
        }
    }
}

// MARK: - Learning Material Recognition Intent

@available(iOS 26.0, *)
struct RecognizeLearningMaterialIntent: AppIntent {
    static var title: LocalizedStringResource = "Find Learning Materials"
    static var description = IntentDescription("Identify Tamil learning materials in images")
    
    @Parameter(title: "Image", description: "Image containing learning materials")
    var image: IntentFile?
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let visualIntelligenceService = VisualIntelligenceService.shared
        
        guard let imageFile = image,
              let imageData = try? Data(contentsOf: imageFile.fileURL),
              let uiImage = UIImage(data: imageData) else {
            return .result(dialog: "Unable to process the image. Please try again.")
        }
        
        do {
            let materialResults = try await visualIntelligenceService.searchLearningMaterials(in: uiImage)
            
            if materialResults.isEmpty {
                return .result(dialog: "No Tamil learning materials found in this image.")
            }
            
            let bestResult = materialResults.first!
            var response = "Found \(bestResult.type) material: \(bestResult.topic)\nLevel: \(bestResult.level)"
            
            if bestResult.audioAvailable {
                response += "\n🔊 Audio available"
            }
            
            return .result(dialog: response)
            
        } catch {
            return .result(dialog: "Error recognizing learning materials: \(error.localizedDescription)")
        }
    }
}

// MARK: - Visual Intelligence App Shortcuts Provider

@available(iOS 26.0, *)
struct VisualIntelligenceAppShortcutsProvider: AppShortcutsProvider {
    static var appShortcuts: [AppShortcut] {
        AppShortcut(
            intent: VisualSearchIntent(),
            phrases: [
                "Search for Tamil content in \(.applicationName)",
                "Find Tamil text with \(.applicationName)",
                "Analyze image for Tamil learning with \(.applicationName)"
            ],
            shortTitle: "Tamil Visual Search",
            systemImageName: "magnifyingglass.circle"
        )
        
        AppShortcut(
            intent: RecognizeTamilTextIntent(),
            phrases: [
                "Recognize Tamil text in \(.applicationName)",
                "Read Tamil text with \(.applicationName)",
                "Translate Tamil text in \(.applicationName)"
            ],
            shortTitle: "Tamil Text Recognition",
            systemImageName: "text.viewfinder"
        )
        
        AppShortcut(
            intent: DiscoverCulturalContentIntent(),
            phrases: [
                "Discover Tamil culture in \(.applicationName)",
                "Find cultural elements with \(.applicationName)",
                "Explore Tamil heritage in \(.applicationName)"
            ],
            shortTitle: "Cultural Discovery",
            systemImageName: "building.columns.circle"
        )
        
        AppShortcut(
            intent: RecognizeLearningMaterialIntent(),
            phrases: [
                "Find learning materials in \(.applicationName)",
                "Identify Tamil lessons with \(.applicationName)",
                "Discover study content in \(.applicationName)"
            ],
            shortTitle: "Learning Material Recognition",
            systemImageName: "book.circle"
        )
    }
}

#endif

// MARK: - Supporting Types

enum VisualSearchType: String, CaseIterable, AppEnum {
    case all = "all"
    case textOnly = "text"
    case culturalOnly = "cultural"
    case learningMaterialsOnly = "materials"
    
    static var typeDisplayRepresentation: TypeDisplayRepresentation {
        TypeDisplayRepresentation(name: "Visual Search Type")
    }
    
    static var caseDisplayRepresentations: [VisualSearchType: DisplayRepresentation] {
        [
            .all: DisplayRepresentation(title: "All Content", subtitle: "Search for all types of Tamil content"),
            .textOnly: DisplayRepresentation(title: "Tamil Text", subtitle: "Search for Tamil text only"),
            .culturalOnly: DisplayRepresentation(title: "Cultural Elements", subtitle: "Search for cultural content only"),
            .learningMaterialsOnly: DisplayRepresentation(title: "Learning Materials", subtitle: "Search for educational content only")
        ]
    }
    
    var includesTextRecognition: Bool {
        return self == .all || self == .textOnly
    }
    
    var includesCulturalContent: Bool {
        return self == .all || self == .culturalOnly
    }
    
    var includesLearningMaterials: Bool {
        return self == .all || self == .learningMaterialsOnly
    }
}

// MARK: - Mock Implementation for Development

class MockVisualIntelligenceIntents {
    static func registerMockIntents() {
        print("🔍 Mock Visual Intelligence Intents registered:")
        print("   - Visual Search Intent")
        print("   - Tamil Text Recognition Intent")
        print("   - Cultural Content Discovery Intent")
        print("   - Learning Material Recognition Intent")
    }
}
