//
//  VisualIntelligenceDemo.swift
//  NIRA
//
//  Created by NIRA Team on 1/22/25.
//  Demo and Testing for Visual Intelligence Features
//

import Foundation
import SwiftUI

// MARK: - Visual Intelligence Demo Service

class VisualIntelligenceDemo: ObservableObject {
    static let shared = VisualIntelligenceDemo()
    
    @Published var demoResults: [VisualSearchResult] = []
    @Published var isRunningDemo = false
    
    private init() {}
    
    // MARK: - Demo Functions
    
    func runTamilTextDemo() async {
        await MainActor.run {
            isRunningDemo = true
        }
        
        // Simulate Tamil text recognition
        let mockTamilResults = [
            TamilScriptResult(
                text: "வணக்கம்",
                confidence: 0.95,
                boundingBox: CGRect(x: 50, y: 100, width: 200, height: 50),
                translation: "Hello/Greetings",
                lessonRecommendations: [
                    LessonRecommendation(id: "1", title: "Basic Greetings", level: .beginner, relevanceScore: 0.9),
                    LessonRecommendation(id: "2", title: "Daily Conversations", level: .elementary, relevanceScore: 0.8)
                ]
            ),
            TamilScriptResult(
                text: "நன்றி",
                confidence: 0.92,
                boundingBox: CGRect(x: 50, y: 200, width: 150, height: 40),
                translation: "Thank you",
                lessonRecommendations: [
                    LessonRecommendation(id: "3", title: "Polite Expressions", level: .beginner, relevanceScore: 0.85)
                ]
            )
        ]
        
        await MainActor.run {
            self.demoResults = mockTamilResults.map { VisualSearchResult.tamilScript($0) }
            self.isRunningDemo = false
        }
    }
    
    func runCulturalContentDemo() async {
        await MainActor.run {
            isRunningDemo = true
        }
        
        // Simulate cultural content recognition
        let mockCulturalResults = [
            CulturalContentResult(
                type: .temple,
                name: "Meenakshi Temple",
                description: "Ancient Hindu temple dedicated to Goddess Meenakshi",
                culturalSignificance: "One of the most important temples in Tamil Nadu, representing Dravidian architecture",
                relatedLessons: [
                    LessonRecommendation(id: "4", title: "Tamil Temples", level: .intermediate, relevanceScore: 0.9),
                    LessonRecommendation(id: "5", title: "Religious Vocabulary", level: .elementary, relevanceScore: 0.8)
                ],
                arExperience: true,
                relevanceScore: 0.88
            ),
            CulturalContentResult(
                type: .festival,
                name: "Pongal Festival",
                description: "Tamil harvest festival celebrating the sun god",
                culturalSignificance: "Most important festival in Tamil culture, marking the harvest season",
                relatedLessons: [
                    LessonRecommendation(id: "6", title: "Tamil Festivals", level: .intermediate, relevanceScore: 0.95),
                    LessonRecommendation(id: "7", title: "Cultural Traditions", level: .upperIntermediate, relevanceScore: 0.7)
                ],
                arExperience: true,
                relevanceScore: 0.92
            )
        ]
        
        await MainActor.run {
            self.demoResults = mockCulturalResults.map { VisualSearchResult.cultural($0) }
            self.isRunningDemo = false
        }
    }
    
    func runLearningMaterialDemo() async {
        await MainActor.run {
            isRunningDemo = true
        }
        
        // Simulate learning material recognition
        let mockLearningResults = [
            LearningMaterialResult(
                type: .vocabulary,
                level: .beginner,
                topic: "Family Members",
                content: "அம்மா (Amma) - Mother, அப்பா (Appa) - Father, அண்ணா (Anna) - Elder Brother",
                practiceExercises: [
                    PracticeExercise(id: "1", type: .multipleChoice, content: "What does 'அம்மா' mean?"),
                    PracticeExercise(id: "2", type: .pronunciation, content: "Practice saying 'அப்பா'")
                ],
                audioAvailable: true,
                relevanceScore: 0.85
            ),
            LearningMaterialResult(
                type: .grammar,
                level: .elementary,
                topic: "Present Tense Verbs",
                content: "வருகிறேன் (varugiren) - I am coming, போகிறேன் (pogiren) - I am going",
                practiceExercises: [
                    PracticeExercise(id: "3", type: .fillInBlank, content: "நான் வீட்டுக்கு _____ (I am going home)"),
                    PracticeExercise(id: "4", type: .conjugation, content: "Conjugate 'வர' (to come) in present tense")
                ],
                audioAvailable: true,
                relevanceScore: 0.78
            )
        ]
        
        await MainActor.run {
            self.demoResults = mockLearningResults.map { VisualSearchResult.learningMaterial($0) }
            self.isRunningDemo = false
        }
    }
    
    func runComprehensiveDemo() async {
        await MainActor.run {
            isRunningDemo = true
        }
        
        // Combine all types of results
        await runTamilTextDemo()
        let textResults = demoResults
        
        await runCulturalContentDemo()
        let culturalResults = demoResults
        
        await runLearningMaterialDemo()
        let learningResults = demoResults
        
        await MainActor.run {
            self.demoResults = textResults + culturalResults + learningResults
            self.isRunningDemo = false
        }
    }
    
    func clearDemoResults() {
        demoResults.removeAll()
    }
    
    // MARK: - Demo Data Generators
    
    func generateMockImage() -> UIImage {
        // Generate a simple mock image with Tamil text
        let size = CGSize(width: 300, height: 200)
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            // Background
            UIColor.systemBackground.setFill()
            context.fill(CGRect(origin: .zero, size: size))
            
            // Tamil text
            let text = "வணக்கம்\nநன்றி"
            let attributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 24, weight: .bold),
                .foregroundColor: UIColor.label
            ]
            
            let attributedText = NSAttributedString(string: text, attributes: attributes)
            let textRect = CGRect(x: 50, y: 50, width: 200, height: 100)
            attributedText.draw(in: textRect)
        }
    }
    
    // MARK: - Testing Utilities
    
    func testVisualIntelligenceIntegration() async -> Bool {
        do {
            // Test service initialization
            let service = VisualIntelligenceService.shared
            
            // Test mock visual search
            let mockImage = generateMockImage()
            let request = VisualSearchRequest(
                image: mockImage,
                includeTextRecognition: true,
                includeCulturalContent: true,
                includeLearningMaterials: true,
                userContext: nil
            )
            
            let results = await service.handleVisualSearchRequest(request)
            
            print("✅ Visual Intelligence test completed")
            print("   - Results count: \(results.count)")
            print("   - Service initialized: \(service != nil)")
            
            return true
        } catch {
            print("❌ Visual Intelligence test failed: \(error)")
            return false
        }
    }
    
    func printFeatureStatus() {
        print("🔍 Visual Intelligence Feature Status:")
        print("   - iOS 26 Available: \(isIOS26Available())")
        print("   - Visual Intelligence Framework: \(isVisualIntelligenceAvailable())")
        print("   - Camera Permission: \(isCameraAvailable())")
        print("   - Photo Library Permission: \(isPhotoLibraryAvailable())")
        print("   - App Intents Registered: \(areAppIntentsRegistered())")
    }
    
    private func isIOS26Available() -> Bool {
        if #available(iOS 26.0, *) {
            return true
        }
        return false
    }
    
    private func isVisualIntelligenceAvailable() -> Bool {
        #if canImport(VisualIntelligence)
        return true
        #else
        return false
        #endif
    }
    
    private func isCameraAvailable() -> Bool {
        return UIImagePickerController.isSourceTypeAvailable(.camera)
    }
    
    private func isPhotoLibraryAvailable() -> Bool {
        return UIImagePickerController.isSourceTypeAvailable(.photoLibrary)
    }
    
    private func areAppIntentsRegistered() -> Bool {
        // In a real implementation, this would check if App Intents are properly registered
        return true
    }
}

// MARK: - Demo View

struct VisualIntelligenceDemoView: View {
    @StateObject private var demo = VisualIntelligenceDemo.shared
    @State private var selectedDemoType = 0
    
    private let demoTypes = ["Tamil Text", "Cultural Content", "Learning Materials", "Comprehensive"]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Demo Type Selector
                Picker("Demo Type", selection: $selectedDemoType) {
                    ForEach(Array(demoTypes.enumerated()), id: \.offset) { index, type in
                        Text(type).tag(index)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding()
                
                // Demo Controls
                VStack(spacing: 12) {
                    Button("Run Demo") {
                        Task {
                            await runSelectedDemo()
                        }
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(demo.isRunningDemo)
                    
                    Button("Clear Results") {
                        demo.clearDemoResults()
                    }
                    .buttonStyle(.bordered)
                    
                    Button("Test Integration") {
                        Task {
                            let success = await demo.testVisualIntelligenceIntegration()
                            print(success ? "✅ Integration test passed" : "❌ Integration test failed")
                        }
                    }
                    .buttonStyle(.bordered)
                }
                
                // Results Display
                if demo.isRunningDemo {
                    ProgressView("Running demo...")
                        .padding()
                } else if !demo.demoResults.isEmpty {
                    ScrollView {
                        LazyVStack(spacing: 12) {
                            ForEach(Array(demo.demoResults.enumerated()), id: \.offset) { index, result in
                                VisualSearchResultCard(result: result)
                                    .padding(.horizontal)
                            }
                        }
                    }
                } else {
                    Text("No results yet. Run a demo to see Visual Intelligence in action!")
                        .foregroundColor(.secondary)
                        .padding()
                }
                
                Spacer()
            }
            .navigationTitle("Visual Intelligence Demo")
            .onAppear {
                demo.printFeatureStatus()
            }
        }
    }
    
    private func runSelectedDemo() async {
        switch selectedDemoType {
        case 0:
            await demo.runTamilTextDemo()
        case 1:
            await demo.runCulturalContentDemo()
        case 2:
            await demo.runLearningMaterialDemo()
        case 3:
            await demo.runComprehensiveDemo()
        default:
            await demo.runComprehensiveDemo()
        }
    }
}

#Preview {
    VisualIntelligenceDemoView()
}
