# Enable Capabilities in Apple Developer Portal - Checklist

## Quick Fix for Provisioning Profile Error

### 🚀 Step 1: Apple Developer Portal
Go to [developer.apple.com](https://developer.apple.com) → Certificates, Identifiers & Profiles → Identifiers

Find your App ID: `com.md.NIRA` and click "Edit"

### ✅ Enable These Capabilities (Check each one):

#### **Required for NIRA Core Features:**
- [ ] **App Intents** - For Siri integration and shortcuts
- [ ] **Background Processing** - For content sync and updates
- [ ] **Camera** - For Visual Intelligence and AR features
- [ ] **Controls and Widgets** - For iOS 18 Control Center widgets
- [ ] **Core ML** - For on-device AI processing
- [ ] **Live Activities** - For real-time learning progress
- [ ] **Photo Library** - For Visual Intelligence image analysis
- [ ] **Siri** - For voice commands and shortcuts
- [ ] **Vision Framework** - For text recognition and image analysis

#### **Optional/Future Features:**
- [ ] **Apple Intelligence** (if available in beta)
- [ ] **Visual Intelligence** (iOS 26 - not yet available)
- [ ] **App Groups** - For data sharing between app and widgets
- [ ] **Keychain Sharing** - For secure API key storage

### 🔄 Step 2: Regenerate Provisioning Profile
1. Go to "Profiles" section
2. Find your development profile for NIRA
3. Click "Edit" → "Generate"
4. Download the new profile

### 📱 Step 3: Update Xcode
1. Open Xcode
2. Go to Xcode → Settings → Accounts
3. Select your Apple ID
4. Click "Download Manual Profiles"
5. Clean and rebuild your project

### 🧪 Step 4: Test Build
Run this command in Terminal from your project directory:
```bash
xcodebuild -project NIRA.xcodeproj -target NIRA -configuration Debug
```

Or simply build in Xcode (⌘B)

## Alternative: Minimal Configuration

If you want to start with minimal capabilities, enable only these essential ones:

### **Minimal Setup:**
- [ ] **App Intents** - Core iOS integration
- [ ] **Camera** - Basic camera access
- [ ] **Core ML** - AI features
- [ ] **Siri** - Voice commands

Then gradually add more capabilities as needed.

## Troubleshooting

### If capabilities are grayed out:
- Make sure you have a paid Apple Developer account
- Some features require iOS 18 beta or special approval
- Contact Apple Developer Support if needed

### If build still fails:
1. Delete Derived Data: Xcode → Settings → Locations → Derived Data → Delete
2. Clean Build Folder: Product → Clean Build Folder (⇧⌘K)
3. Restart Xcode
4. Try building again

### If you can't enable certain capabilities:
- **Apple Intelligence**: Requires special approval from Apple
- **Visual Intelligence**: Not yet available (iOS 26 beta)
- **Some features**: May be region-restricted or require specific account types

## Success Indicators

✅ No provisioning profile errors when building
✅ All required capabilities enabled in Developer Portal
✅ New provisioning profile downloaded and installed
✅ Project builds successfully in Xcode

## Quick Commands

**Clean and rebuild:**
```bash
# Clean
xcodebuild clean -project NIRA.xcodeproj -target NIRA

# Build
xcodebuild -project NIRA.xcodeproj -target NIRA -configuration Debug
```

**Check provisioning profiles:**
```bash
# List installed profiles
ls ~/Library/MobileDevice/Provisioning\ Profiles/

# Remove all profiles (if needed)
rm ~/Library/MobileDevice/Provisioning\ Profiles/*
```

Once you complete these steps, your NIRA app should build without provisioning profile errors and be ready for iOS 18+ features!
