# Fix Provisioning Profile Error - Step by Step Guide

## Problem
Your provisioning profile doesn't include the required entitlements for iOS 18+ features and Visual Intelligence.

## Solution Steps

### Step 1: Apple Developer Portal Configuration

1. **Go to Apple Developer Portal**
   - Visit [developer.apple.com](https://developer.apple.com)
   - Sign in with your Apple Developer account

2. **Navigate to App Identifier**
   - Go to "Certificates, Identifiers & Profiles"
   - Click "Identifiers" → "App IDs"
   - Find and select `com.md.NIRA`

3. **Enable Required Capabilities**
   Click "Edit" and enable these capabilities:
   
   **iOS 18 Features:**
   - ✅ App Intents
   - ✅ Apple Intelligence (if available in beta)
   - ✅ Background Processing
   - ✅ Camera
   - ✅ Controls and Widgets
   - ✅ Core ML
   - ✅ Live Activities
   - ✅ Photo Library
   - ✅ Siri
   - ✅ Vision Framework
   
   **iOS 26 Features (when available):**
   - ✅ Visual Intelligence
   
   **Additional:**
   - ✅ App Groups
   - ✅ Keychain Sharing

4. **Save Changes**
   - Click "Save" to update your App ID

### Step 2: Update Provisioning Profile

1. **Go to Profiles**
   - In the same portal, click "Profiles"
   - Find your development profile for NIRA

2. **Regenerate Profile**
   - Click "Edit" on your profile
   - Ensure all new capabilities are included
   - Click "Generate" to create new profile

3. **Download New Profile**
   - Download the updated `.mobileprovision` file

### Step 3: Update Xcode Project

#### Option A: Automatic (Recommended)
1. **Open Xcode**
2. **Go to Xcode → Settings → Accounts**
3. **Select your Apple ID**
4. **Click "Download Manual Profiles"**
5. **Clean and rebuild your project**

#### Option B: Manual Installation
1. **Double-click the downloaded `.mobileprovision` file**
2. **It will install automatically**
3. **Restart Xcode**

### Step 4: Configure Project Settings

1. **Open NIRA.xcodeproj in Xcode**
2. **Select NIRA target**
3. **Go to "Signing & Capabilities" tab**
4. **Verify correct team is selected**
5. **Add capabilities manually if needed:**

   Click "+" and add:
   - App Intents
   - Background Processing
   - Camera
   - Controls and Widgets
   - Core ML
   - Live Activities
   - Photo Library Access
   - Siri
   - Vision Framework

### Step 5: Verify Entitlements File

Your `NIRA.entitlements` file should look like this:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- Existing entitlements -->
    <key>aps-environment</key>
    <string>development</string>
    
    <!-- iOS 18+ Features -->
    <key>com.apple.developer.appintents-extension</key>
    <true/>
    <key>com.apple.developer.apple-intelligence</key>
    <true/>
    <key>com.apple.developer.live-activities</key>
    <true/>
    <key>com.apple.developer.controls</key>
    <true/>
    <key>com.apple.developer.widgets</key>
    <true/>
    
    <!-- Camera and ML -->
    <key>com.apple.developer.camera</key>
    <true/>
    <key>com.apple.developer.photo-library</key>
    <true/>
    <key>com.apple.developer.coreml</key>
    <true/>
    <key>com.apple.developer.vision</key>
    <true/>
    
    <!-- Background Processing -->
    <key>com.apple.developer.background-processing</key>
    <array>
        <string>com.nira.content-sync</string>
        <string>com.nira.model-update</string>
    </array>
    
    <!-- Siri Integration -->
    <key>com.apple.developer.siri</key>
    <true/>
    <key>com.apple.developer.shortcuts</key>
    <true/>
    
    <!-- App Groups -->
    <key>com.apple.security.application-groups</key>
    <array>
        <string>group.com.nira.shared</string>
    </array>
    
    <!-- Keychain Sharing -->
    <key>keychain-access-groups</key>
    <array>
        <string>$(AppIdentifierPrefix)com.nira.keychain</string>
    </array>
</dict>
</plist>
```

### Step 6: Test the Build

1. **Clean Build Folder**
   - Product → Clean Build Folder (⇧⌘K)

2. **Build Project**
   - Product → Build (⌘B)

3. **Verify No Errors**
   - Check that provisioning profile errors are resolved

## Alternative: Temporary Development Solution

If you can't enable all entitlements immediately, you can temporarily disable them:

### Option 1: Comment Out Entitlements
Keep the entitlements in the file but commented out:
```xml
<!-- DISABLED FOR DEVELOPMENT
<key>com.apple.developer.visual-intelligence</key>
<true/>
-->
```

### Option 2: Use Conditional Compilation
The code already includes conditional compilation:
```swift
#if canImport(VisualIntelligence)
// Visual Intelligence code
#else
// Mock implementation
#endif
```

## Troubleshooting

### If you still get errors:

1. **Check Team Settings**
   - Ensure correct Apple Developer team is selected
   - Verify you have proper permissions in the team

2. **Clear Derived Data**
   - Xcode → Settings → Locations → Derived Data → Delete

3. **Reset Provisioning Profiles**
   - Delete all profiles: `~/Library/MobileDevice/Provisioning Profiles/`
   - Re-download from Apple Developer Portal

4. **Contact Apple Developer Support**
   - If entitlements aren't available, they might be in beta

## Notes

- **Visual Intelligence** requires iOS 26 beta (not yet available)
- **Apple Intelligence** requires special approval from Apple
- Some features may only be available to paid Apple Developer accounts
- Beta features may not be available in all regions

## Success Indicators

✅ Project builds without provisioning profile errors
✅ All required capabilities are enabled in Developer Portal
✅ Entitlements file matches your App ID configuration
✅ Xcode shows correct provisioning profile in project settings

Once these steps are complete, your NIRA app will be ready for iOS 18+ features and Visual Intelligence when iOS 26 becomes available.
